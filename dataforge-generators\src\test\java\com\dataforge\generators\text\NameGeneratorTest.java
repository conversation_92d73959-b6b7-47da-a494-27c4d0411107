package com.dataforge.generators.text;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * 姓名生成器单元测试
 */
@DisplayName("姓名生成器测试")
class NameGeneratorTest {

    private NameGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new NameGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext(parameters, 12345L);
    }

    @Test
    @DisplayName("基本姓名生成测试")
    void testBasicGeneration() {
        String name = generator.generate(context);

        assertThat(name).isNotNull();
        assertThat(name).isNotEmpty();
        assertThat(name.trim()).isNotEmpty();
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证有效性")
    void testRepeatedGeneration() {
        String name = generator.generate(context);

        assertThat(name).isNotNull();
        assertThat(name).isNotEmpty();
        assertThat(generator.validate(name)).isTrue();
    }

    @Test
    @DisplayName("指定性别生成测试")
    void testGenerationWithGender() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("gender", "male");
        GenerationContext maleContext = new GenerationContext(parameters, 12345L);

        String maleName = generator.generate(maleContext);

        assertThat(maleName).isNotNull();
        assertThat(generator.validate(maleName)).isTrue();

        // 测试女性姓名
        parameters.put("gender", "female");
        GenerationContext femaleContext = new GenerationContext(parameters, 12345L);

        String femaleName = generator.generate(femaleContext);
        assertThat(femaleName).isNotNull();
        assertThat(generator.validate(femaleName)).isTrue();
    }

    @Test
    @DisplayName("指定姓名类型生成测试")
    void testGenerationWithNameType() {
        String[] nameTypes = { "CHINESE", "ENGLISH", "JAPANESE", "KOREAN" };

        for (String nameType : nameTypes) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("nameType", nameType);
            GenerationContext typeContext = new GenerationContext(parameters, 12345L);

            String name = generator.generate(typeContext);
            assertThat(name).isNotNull();
            assertThat(generator.validate(name)).isTrue();
        }
    }

    @Test
    @DisplayName("中文姓名生成测试")
    void testChineseNameGeneration() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("nameType", "CHINESE");
        GenerationContext chineseContext = new GenerationContext(parameters, 12345L);

        String chineseName = generator.generate(chineseContext);

        assertThat(chineseName).isNotNull();
        assertThat(chineseName.length()).isBetween(2, 4); // 中文姓名通常2-4个字符
        assertThat(generator.validate(chineseName)).isTrue();
    }

    @Test
    @DisplayName("英文姓名生成测试")
    void testEnglishNameGeneration() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("nameType", "ENGLISH");
        GenerationContext englishContext = new GenerationContext(parameters, 12345L);

        String englishName = generator.generate(englishContext);

        assertThat(englishName).isNotNull();
        assertThat(englishName).matches("[A-Za-z\\s]+"); // 只包含字母和空格
        assertThat(englishName).contains(" "); // 应该包含空格分隔名和姓
        assertThat(generator.validate(englishName)).isTrue();
    }

    @Test
    @DisplayName("姓名校验测试 - 有效姓名")
    void testValidationWithValidName() {
        String[] validNames = {
                "张三",
                "李小明",
                "John Smith",
                "Mary Johnson",
                "田中太郎",
                "김민수"
        };

        for (String name : validNames) {
            assertThat(generator.validate(name)).isTrue();
        }
    }

    @Test
    @DisplayName("姓名校验测试 - 无效姓名")
    void testValidationWithInvalidName() {
        String[] invalidNames = {
                "",
                "   ",
                "123",
                "!@#$%",
                "a", // 太短
                "这是一个非常非常长的姓名超过了正常的长度限制" // 太长
        };

        for (String name : invalidNames) {
            assertThat(generator.validate(name)).isFalse();
        }

        // 测试null值
        assertThat(generator.validate(null)).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("name");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        List<GeneratorParameter> parameters = generator.getSupportedParameters();

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("gender", "nameType", "includeMiddleName");
    }

    @Test
    @DisplayName("生成数据格式一致性测试")
    void testGeneratedDataConsistency() {
        for (int i = 0; i < 100; i++) {
            String name = generator.generate(context);
            assertThat(name).isNotNull();
            assertThat(name).isNotEmpty();
            assertThat(generator.validate(name)).isTrue();
        }
    }

    @Test
    @DisplayName("包含中间名测试")
    void testIncludeMiddleName() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("nameType", "ENGLISH");
        parameters.put("includeMiddleName", true);
        GenerationContext middleNameContext = new GenerationContext(parameters, 12345L);

        String nameWithMiddle = generator.generate(middleNameContext);

        assertThat(nameWithMiddle).isNotNull();
        // 包含中间名的英文姓名应该有至少两个空格
        long spaceCount = nameWithMiddle.chars().filter(ch -> ch == ' ').count();
        assertThat(spaceCount).isGreaterThanOrEqualTo(1);
    }

    @Test
    @DisplayName("姓名长度限制测试")
    void testNameLengthLimit() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("maxLength", 10);
        GenerationContext lengthContext = new GenerationContext(parameters, 12345L);

        String name = generator.generate(lengthContext);

        assertThat(name).isNotNull();
        assertThat(name.length()).isLessThanOrEqualTo(10);
    }

    @Test
    @DisplayName("姓名多样性测试")
    void testNameDiversity() {
        java.util.Set<String> names = new java.util.HashSet<>();

        for (int i = 0; i < 100; i++) {
            String name = generator.generate(context);
            names.add(name);
        }

        // 应该生成多种不同的姓名
        assertThat(names.size()).isGreaterThan(80);
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("nameType", "INVALID_TYPE");
        parameters.put("gender", "INVALID_GENDER");
        GenerationContext invalidContext = new GenerationContext(parameters, 12345L);

        // 应该能够处理无效参数并生成有效的姓名
        String name = generator.generate(invalidContext);
        assertThat(name).isNotNull();
        assertThat(generator.validate(name)).isTrue();
    }

    @Test
    @DisplayName("性能测试")
    void testPerformance() {
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 10000; i++) {
            String name = generator.generate(context);
            assertThat(name).isNotNull();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertThat(duration).isLessThan(3000); // 应该在3秒内完成
    }

    @Test
    @DisplayName("特殊字符处理测试")
    void testSpecialCharacterHandling() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("allowSpecialChars", false);
        GenerationContext noSpecialContext = new GenerationContext(parameters, 12345L);

        String name = generator.generate(noSpecialContext);

        assertThat(name).isNotNull();
        // 不应该包含特殊字符
        assertThat(name).matches("[\\p{L}\\s]+"); // 只包含字母和空格
    }

    @Test
    @DisplayName("种子一致性测试")
    void testSeedConsistency() {
        Map<String, Object> parameters = new HashMap<>();
        GenerationContext context1 = new GenerationContext(parameters, 12345L);
        GenerationContext context2 = new GenerationContext(parameters, 12345L);
        
        String name1 = generator.generate(context1);
        String name2 = generator.generate(context2);
        
        // 相同种子应该产生相同结果
        assertThat(name1).isEqualTo(name2);
    }