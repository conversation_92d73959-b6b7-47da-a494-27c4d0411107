package com.dataforge.generators.identifier;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static org.assertj.core.api.Assertions.*;

/**
 * 身份证号生成器单元测试
 */
@DisplayName("身份证号生成器测试")
class IdCardNumberGeneratorTest {

    private IdCardNumberGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new IdCardNumberGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext(parameters, 12345L);
    }

    @Test
    @DisplayName("基本身份证号生成测试")
    void testBasicGeneration() {
        String idCard = generator.generate(context);

        assertThat(idCard).isNotNull();
        assertThat(idCard).hasSize(18);
        assertThat(idCard).matches("\\d{18}");
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证随机性")
    void testRepeatedGeneration() {
        String idCard = generator.generate(context);

        assertThat(idCard).isNotNull();
        assertThat(idCard).hasSize(18);
        assertThat(generator.validate(idCard)).isTrue();
    }

    @Test
    @DisplayName("指定地区代码生成测试")
    void testGenerationWithRegionCode() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("regionCode", "110000"); // 北京
        GenerationContext contextWithRegion = new GenerationContext(parameters, 12345L);

        String idCard = generator.generate(contextWithRegion);

        assertThat(idCard).isNotNull();
        assertThat(idCard).startsWith("110");
        assertThat(generator.validate(idCard)).isTrue();
    }

    @Test
    @DisplayName("指定性别生成测试")
    void testGenerationWithGender() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("gender", "male");
        GenerationContext contextWithGender = new GenerationContext(parameters, 12345L);

        String idCard = generator.generate(contextWithGender);

        assertThat(idCard).isNotNull();
        // 第17位数字为奇数表示男性
        int genderDigit = Character.getNumericValue(idCard.charAt(16));
        assertThat(genderDigit % 2).isEqualTo(1);
        assertThat(generator.validate(idCard)).isTrue();
    }

    @Test
    @DisplayName("指定出生年份范围生成测试")
    void testGenerationWithBirthYearRange() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("minBirthYear", 1990);
        parameters.put("maxBirthYear", 2000);
        GenerationContext contextWithYear = new GenerationContext(parameters, 12345L);

        String idCard = generator.generate(contextWithYear);

        assertThat(idCard).isNotNull();
        String birthYear = idCard.substring(6, 10);
        int year = Integer.parseInt(birthYear);
        assertThat(year).isBetween(1990, 2000);
        assertThat(generator.validate(idCard)).isTrue();
    }

    @Test
    @DisplayName("身份证号校验测试 - 有效号码")
    void testValidationWithValidIdCard() {
        // 使用已知的有效身份证号进行测试
        String validIdCard = "11010519491231002X";
        assertThat(generator.validate(validIdCard)).isTrue();
    }

    @Test
    @DisplayName("身份证号校验测试 - 无效号码")
    void testValidationWithInvalidIdCard() {
        // 测试长度不正确的身份证号
        assertThat(generator.validate("1234567890")).isFalse();

        // 测试包含非数字字符的身份证号（除了最后一位可能是X）
        assertThat(generator.validate("11010519491231002A")).isFalse();

        // 测试校验位错误的身份证号
        assertThat(generator.validate("110105194912310021")).isFalse();
    }

    @Test
    @DisplayName("身份证号校验测试 - 空值和null")
    void testValidationWithNullAndEmpty() {
        assertThat(generator.validate(null)).isFalse();
        assertThat(generator.validate("")).isFalse();
        assertThat(generator.validate("   ")).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("idcard");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        List<GeneratorParameter> parameters = generator.getSupportedParameters();

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("regionCode", "gender", "minBirthYear", "maxBirthYear");
    }

    @Test
    @DisplayName("生成数据格式一致性测试")
    void testGeneratedDataConsistency() {
        Pattern idCardPattern = Pattern.compile("\\d{17}[\\dX]");

        for (int i = 0; i < 100; i++) {
            String idCard = generator.generate(context);
            assertThat(idCard).matches(idCardPattern);
            assertThat(generator.validate(idCard)).isTrue();
        }
    }

    @Test
    @DisplayName("边界值测试 - 最小最大出生年份")
    void testBoundaryValues() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("minBirthYear", 1900);
        parameters.put("maxBirthYear", 1900);
        GenerationContext boundaryContext = new GenerationContext(parameters, 12345L);

        String idCard = generator.generate(boundaryContext);
        String birthYear = idCard.substring(6, 10);
        assertThat(birthYear).isEqualTo("1900");
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("minBirthYear", 2100); // 未来年份
        parameters.put("maxBirthYear", 1800); // 最小值大于最大值
        GenerationContext invalidContext = new GenerationContext(parameters, 12345L);

        // 应该能够处理无效参数并生成有效的身份证号
        String idCard = generator.generate(invalidContext);
        assertThat(idCard).isNotNull();
        assertThat(generator.validate(idCard)).isTrue();
    }
}